<?php

namespace App\Http\Controllers\Course;

use App\Http\Controllers\Controller;
use App\Http\Requests\Material\StoreRequest;
use App\Http\Requests\Material\UpdateRequest;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\Material;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class MaterialController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize('show-course', Material::class);
        $keyword = $request->input('search');

        $query = Material::query()
            ->with('lesson')
            ->with('lesson.course');

        if($keyword != null){
            $query = $query->where('title', 'like', "%$keyword%");
        }

        $materials = $query
            ->orderBy('order', 'asc') // Order by order column first
            ->orderBy('created_at', 'asc') // Then by creation time to preserve CSV import order
            ->paginate(100);

        return view('material.index', compact('materials', 'keyword'));
    }

    public function create(Request $request)
    {
        $this->authorize('create-course', Material::class);
        $courses = Course::all();
        $lessons = Lesson::all();
        $title = $request->input('title', '');
        $content = $request->input('content', '');
        return view('material.create', compact('lessons', 'courses', 'title', 'content'));
    }

    public function store(StoreRequest $request)
    {
        $this->authorize('create-course', Material::class);

        $title = $request->input('title');
        $order = $request->input('order');

        // If order is provided, update title with order prefix
        if ($order) {
            // Remove existing "Bài X - " prefix if it exists to avoid duplication
            $cleanTitle = preg_replace('/^Bài\s+\d+\s*-\s*/', '', $title);
            $title = "Bài " . $order . " - " . $cleanTitle;
        }

        Material::query()->create([
            'title' => $title,
//            'type' => $request->input('type'),
            'content' => $request->input('content'),
            'content_url' => $request->input('content_url'),
            'document_url' => $request->input('document_url'),
            'lesson_id' => $request->input('lesson'),
            'order' => $order,
        ]);

        $this->flashMessage('check', 'Material successfully added!', 'success');
        return redirect()->route('material');
    }

    public function edit($id)
    {
        $this->authorize('edit-course', Lesson::class);
        $material = Material::query()
            ->find($id);
        $courses = Course::all();
        $lessons = Lesson::all();

        return view('material.edit',compact('material', 'courses', 'lessons'));
    }

    public function update(UpdateRequest $request, $id)
    {
        $this->authorize('edit-course', Lesson::class);
        $material = Material::query()->where('id', $id)->first();

        if(!$material){
            $this->flashMessage('warning', 'Material not found!', 'danger');
            return redirect()->route('material');
        }

        $title = $request->input('title');
        $order = $request->input('order');

        // If order is provided, update title with order prefix
        if ($order) {
            // Remove existing "Bài X - " prefix if it exists to avoid duplication
            $cleanTitle = preg_replace('/^Bài\s+\d+\s*-\s*/', '', $title);
            $title = "Bài " . $order . " - " . $cleanTitle;
        }

        $material->fill([
            'title' => $title,
//            'type' => $request->input('type'),
            'content' => $request->input('content'),
            'content_url' => $request->input('content_url'),
            'document_url' => $request->input('document_url'),
            'lesson_id' => $request->input('lesson'),
            'order' => $order,
        ]);
        $material->save();

        $this->flashMessage('check', 'Material successfully edited!', 'success');
        return redirect()->route('material');
    }

    public function destroy($id): RedirectResponse
    {
        $this->authorize('destroy-course', Material::class);
        $material = Material::query()->find($id);

        if(!$material){
            $this->flashMessage('warning', 'Material not found!', 'danger');
            return redirect()->route('material');
        }

        $material->delete();
        $this->flashMessage('check', 'Material successfully deleted!', 'success');

        return redirect()->route('material');
    }

    /**
     * Bulk delete materials
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function bulkDestroy(Request $request): RedirectResponse
    {
        $this->authorize('destroy-course', Material::class);
        $ids = explode(',', $request->input('ids'));

        if (empty($ids)) {
            $this->flashMessage('warning', 'No materials selected!', 'danger');
            return redirect()->route('material');
        }

        $deletedCount = 0;
        foreach ($ids as $id) {
            $material = Material::query()->find($id);
            if ($material) {
                $material->delete();
                $deletedCount++;
            }
        }

        if ($deletedCount > 0) {
            $this->flashMessage('check', $deletedCount . ' materials successfully deleted!', 'success');
        } else {
            $this->flashMessage('warning', 'No materials were deleted!', 'danger');
        }

        return redirect()->route('material');
    }
}
