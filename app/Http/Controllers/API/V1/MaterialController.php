<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Models\Material;
use App\Models\Account;
use Illuminate\Http\Request;

class MaterialController extends Controller
{
    /**
     * Get materials for a specific account with account-type-specific titles
     *
     * @param Request $request
     * @return array
     */
    public function getMaterials(Request $request)
    {
        $accountId = $request->input('account_id');
        $softwareType = $request->input('software_type');
        $courseId = $request->input('course_id'); // Optional filter

        if (!$accountId || !isset($softwareType)) {
            return [
                'error' => true,
                'message' => 'Account ID and software type are required'
            ];
        }

        // Get account and determine account type
        $account = Account::find($accountId);
        if (!$account) {
            return [
                'error' => true,
                'message' => 'Account not found'
            ];
        }

        $accountTypeId = null;
        if ($softwareType == Account::SOFTWARE_MAMNON && $account->is_mamnon) {
            $accountTypeId = $account->mamnon_account_type_id;
        } elseif ($softwareType == Account::SOFTWARE_TIEUHOC && $account->is_tieuhoc) {
            $accountTypeId = $account->tieuhoc_account_type_id;
        }

        // Build query for materials
        $query = Material::query()
            ->with('lesson')
            ->with('lesson.course');

        // Filter by course if specified
        if ($courseId) {
            $query->whereHas('lesson.course', function($q) use ($courseId) {
                $q->where('id', $courseId);
            });
        }

        // Apply account-type-specific ordering
        if ($accountTypeId) {
            $query->leftJoin('account_type_material_orders', function($join) use ($accountTypeId) {
                $join->on('materials.id', '=', 'account_type_material_orders.material_id')
                     ->where('account_type_material_orders.account_type_id', '=', $accountTypeId);
            })
            ->orderByRaw('COALESCE(account_type_material_orders.order, materials.order) IS NULL, COALESCE(account_type_material_orders.order, materials.order) ASC')
            ->orderBy('materials.created_at', 'asc')
            ->select('materials.*');
        } else {
            // Default global ordering
            $query->orderByRaw('`order` IS NULL, `order` ASC')
                  ->orderBy('created_at', 'asc');
        }

        $materials = $query->get();

        // Transform materials with account-type-specific titles
        $transformedMaterials = $materials->map(function($material) use ($accountTypeId) {
            return [
                'id' => $material->id,
                'title' => $material->getDisplayTitleForAccountType($accountTypeId),
                'content' => $material->content,
                'content_url' => $material->content_url,
                'document_url' => $material->document_url,
                'order' => $accountTypeId ? $material->getOrderForAccountType($accountTypeId) : $material->order,
                'lesson' => [
                    'id' => $material->lesson->id,
                    'title' => $material->lesson->title,
                    'course' => [
                        'id' => $material->lesson->course->id,
                        'title' => $material->lesson->course->title_en,
                        'key' => $material->lesson->course->key,
                    ]
                ]
            ];
        });

        return [
            'error' => false,
            'materials' => $transformedMaterials,
            'account_type_id' => $accountTypeId
        ];
    }

    /**
     * Get a single material with account-type-specific title
     *
     * @param Request $request
     * @param int $materialId
     * @return array
     */
    public function getMaterial(Request $request, $materialId)
    {
        $accountId = $request->input('account_id');
        $softwareType = $request->input('software_type');

        $material = Material::with('lesson.course')->find($materialId);
        if (!$material) {
            return [
                'error' => true,
                'message' => 'Material not found'
            ];
        }

        $accountTypeId = null;
        if ($accountId && isset($softwareType)) {
            $account = Account::find($accountId);
            if ($account) {
                if ($softwareType == Account::SOFTWARE_MAMNON && $account->is_mamnon) {
                    $accountTypeId = $account->mamnon_account_type_id;
                } elseif ($softwareType == Account::SOFTWARE_TIEUHOC && $account->is_tieuhoc) {
                    $accountTypeId = $account->tieuhoc_account_type_id;
                }
            }
        }

        return [
            'error' => false,
            'material' => [
                'id' => $material->id,
                'title' => $material->getDisplayTitleForAccountType($accountTypeId),
                'content' => $material->content,
                'content_url' => $material->content_url,
                'document_url' => $material->document_url,
                'order' => $accountTypeId ? $material->getOrderForAccountType($accountTypeId) : $material->order,
                'lesson' => [
                    'id' => $material->lesson->id,
                    'title' => $material->lesson->title,
                    'course' => [
                        'id' => $material->lesson->course->id,
                        'title' => $material->lesson->course->title_en,
                        'key' => $material->lesson->course->key,
                    ]
                ]
            ]
        ];
    }
}
