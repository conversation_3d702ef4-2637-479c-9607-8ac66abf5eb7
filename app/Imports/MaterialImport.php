<?php

namespace App\Imports;

use App\Models\Material;
use App\Models\Course;
use App\Models\Lesson;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MaterialImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    private $logicErrors;
    private $count;
    private $totalCount;
    private $autoNumbering;
    private $accountTypeId;

    public function __construct($autoNumbering = true, $accountTypeId = null)
    {
        $this->logicErrors = null;
        $this->count = 0;
        $this->totalCount = 0;
        $this->autoNumbering = $autoNumbering;
        $this->accountTypeId = $accountTypeId;
    }

    public function collection(Collection $collection)
    {
        DB::beginTransaction();

        $this->totalCount = $collection->count();

        foreach ($collection as $rowIndex => $row) {
            if($row == null){
                continue;
            }

            // init value - matching the format from your screenshot
            $values = array_values($row->toArray());
            $materialTitle = $values[0] ?? null;  // Tiêu đề
            $contentUrl = $values[1] ?? null;     // URL
            $content = $values[2] ?? null;        // Nội dung
            $documentUrl = $values[3] ?? null;    // Giáo án
            $courseKey = $values[4] ?? null;      // Khóa học

            // Validate required fields
            if (empty($materialTitle)) {
                $this->logicErrors = "Tiêu đề is required at row " . ($rowIndex + 2); // +2 because of header row
                DB::rollBack();
                return;
            }

            if (empty($courseKey)) {
                $this->logicErrors = "Khóa học is required at row " . ($rowIndex + 2);
                DB::rollBack();
                return;
            }

            // Find course by key
            $course = Course::where('key', $courseKey)->first();
            if (!$course) {
                $this->logicErrors = "Course with key '{$courseKey}' not found at row " . ($rowIndex + 2);
                DB::rollBack();
                return;
            }

            // Find or create default lesson for the course
            $lesson = Lesson::where('course_id', $course->id)->first();

            if (!$lesson) {
                // Create default lesson for the course
                $lesson = new Lesson();
                $lesson->fill([
                    'title' => $course->title_en . ' - Materials',
                    'course_id' => $course->id,
                    'description' => 'Default lesson for course materials',
                ]);
                $lesson->save();
            }

            // No duplicate check - materials can have same names but different content
            // and materials are often reused across different contexts

            // Extract number from content based on course material prefix and add prefix to title
            $prefixedTitle = $materialTitle;
            $order = null;

            if ($this->autoNumbering && !empty($content) && !empty($course->material_prefix)) {
                // Check if content starts with the material prefix
                if (strpos($content, $course->material_prefix) === 0) {
                    // Remove the prefix and extract the first number
                    $remaining = substr($content, strlen($course->material_prefix));
                    if (preg_match('/(\d+)/', $remaining, $matches)) {
                        // Convert to integer to remove leading zeros (e.g., "02" -> 2)
                        $order = (int) $matches[1];
                        $prefixedTitle = "Bài " . $order . " - " . $materialTitle;
                    }
                }
            }

            // Create material with precise timestamp to maintain order
            $material = new Material();
            $material->fill([
                'lesson_id' => $lesson->id,
                'title' => $prefixedTitle,
                // 'type' => 'document', // Type field not used - consistent with manual creation
                'content' => $content,
                'content_url' => $contentUrl,
                'document_url' => $documentUrl,
                'order' => $this->accountTypeId ? null : $order, // Only set global order if no account type specified
            ]);

            // Set precise timestamp to maintain CSV row order
            $baseTime = now();
            $material->created_at = $baseTime->copy()->addMicroseconds($rowIndex * 1000);
            $material->updated_at = $material->created_at;

            $material->save();

            // If account type is specified and we have an order, set account-type-specific order
            if ($this->accountTypeId && $order) {
                $material->setOrderForAccountType($this->accountTypeId, $order);
            }

            $this->count++;
        }

        DB::commit();
    }

    public function getLogicErrors()
    {
        return $this->logicErrors;
    }

    public function importedCount()
    {
        return $this->count;
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
