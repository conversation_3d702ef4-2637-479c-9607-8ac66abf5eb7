<?php

namespace App\Exports;

use App\Models\Material;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

class MaterialExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;
    private $stt;

    public function __construct()
    {
        $this->stt = 0;
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return Material::query()
            ->select([
                'id',
                'title',
                'content_url',
                'content',
                'document_url',
                'lesson_id',
                'order',
                'created_at'
            ])
            ->with(['lesson.course'])
            ->orderByRaw('`order` IS NULL, `order` ASC') // Order by order column first (nulls last)
            ->orderBy('created_at', 'asc') // Then by creation time to maintain import order
            ->get();
    }

    public function headings(): array
    {
        return [
            'STT',
            'Thứ tự',
            'Tiêu đề',
            'URL',
            'Nội dung',
            'Giáo án',
            'Khóa học',
            'Tên khóa học',
            'Tên bài học'
        ];
    }

    public function map($material): array
    {
        $courseKey = '';
        $courseName = '';
        $lessonName = '';

        if ($material->lesson && $material->lesson->course) {
            $courseKey = $material->lesson->course->key ?: $material->lesson->course->id;
            $courseName = $material->lesson->course->title_en ?? '';
            $lessonName = $material->lesson->title ?? '';
        }

        return [
            ++$this->stt,
            $material->order ?? '',
            $material->title,
            $material->content_url,
            $material->content,
            $material->document_url,
            $courseKey,
            $courseName,
            $lessonName
        ];
    }
}
