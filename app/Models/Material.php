<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Material extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['lesson_id', 'title', 'type', 'content', 'content_url', 'deleted_at', 'document_url', 'order'];

    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    /**
     * Get the account type material orders for this material
     */
    public function accountTypeOrders()
    {
        return $this->hasMany(AccountTypeMaterialOrder::class);
    }

    /**
     * Get the order for a specific account type
     *
     * @param int $accountTypeId
     * @return int|null
     */
    public function getOrderForAccountType($accountTypeId)
    {
        $orderRecord = $this->accountTypeOrders()->where('account_type_id', $accountTypeId)->first();
        return $orderRecord ? $orderRecord->order : $this->order; // Fallback to global order
    }

    /**
     * Get the display title with order prefix for a specific account type
     *
     * @param int|null $accountTypeId
     * @return string
     */
    public function getDisplayTitleForAccountType($accountTypeId = null)
    {
        $order = $accountTypeId ? $this->getOrderForAccountType($accountTypeId) : $this->order;

        if ($order) {
            // Remove existing "Bài X - " prefix if it exists to avoid duplication
            $cleanTitle = preg_replace('/^Bài\s+\d+\s*-\s*/', '', $this->title);
            return "Bài " . $order . " - " . $cleanTitle;
        }

        return $this->title;
    }

    /**
     * Get the display title with order prefix if order is set (backward compatibility)
     *
     * @return string
     */
    public function getDisplayTitleAttribute()
    {
        return $this->getDisplayTitleForAccountType();
    }

    /**
     * Get the base title without the "Bài X - " prefix
     *
     * @return string
     */
    public function getBaseTitleAttribute()
    {
        return preg_replace('/^Bài\s+\d+\s*-\s*/', '', $this->title);
    }

    /**
     * Set the order for a specific account type
     *
     * @param int $accountTypeId
     * @param int|null $order
     * @return void
     */
    public function setOrderForAccountType($accountTypeId, $order)
    {
        AccountTypeMaterialOrder::updateOrCreate(
            [
                'account_type_id' => $accountTypeId,
                'material_id' => $this->id
            ],
            [
                'order' => $order
            ]
        );
    }

    /**
     * Update the title based on the order (backward compatibility)
     *
     * @param int|null $order
     * @return void
     */
    public function updateTitleWithOrder($order = null)
    {
        $order = $order ?? $this->order;

        if ($order) {
            $baseTitle = $this->getBaseTitleAttribute();
            $this->title = "Bài " . $order . " - " . $baseTitle;
        }
    }

    /**
     * Update the title based on the order for a specific account type
     *
     * @param int $accountTypeId
     * @param int|null $order
     * @return void
     */
    public function updateTitleWithOrderForAccountType($accountTypeId, $order = null)
    {
        $order = $order ?? $this->getOrderForAccountType($accountTypeId);

        if ($order) {
            $baseTitle = $this->getBaseTitleAttribute();
            $this->title = "Bài " . $order . " - " . $baseTitle;
            $this->save();
        }
    }
}
