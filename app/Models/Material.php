<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Material extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['lesson_id', 'title', 'type', 'content', 'content_url', 'deleted_at', 'document_url', 'order'];

    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    /**
     * Get the account type material orders for this material
     */
    public function accountTypeOrders()
    {
        return $this->hasMany(AccountTypeMaterialOrder::class);
    }

    /**
     * Get the order for a specific account type
     *
     * @param int $accountTypeId
     * @return int|null
     */
    public function getOrderForAccountType($accountTypeId)
    {
        $orderRecord = $this->accountTypeOrders()->where('account_type_id', $accountTypeId)->first();
        return $orderRecord ? $orderRecord->order : $this->order; // Fallback to global order
    }

    /**
     * Get the display title with order prefix for a specific account type
     *
     * @param int|null $accountTypeId
     * @return string
     */
    public function getDisplayTitleForAccountType($accountTypeId = null)
    {
        $order = $accountTypeId ? $this->getOrderForAccountType($accountTypeId) : $this->order;

        if ($order) {
            // Remove existing "Bài X - " prefix if it exists to avoid duplication
            $cleanTitle = preg_replace('/^Bài\s+\d+\s*-\s*/', '', $this->title);
            return "Bài " . $order . " - " . $cleanTitle;
        }

        return $this->title;
    }

    /**
     * Get the display title with order prefix if order is set (backward compatibility)
     * Now automatically detects user's account type
     *
     * @return string
     */
    public function getDisplayTitleAttribute()
    {
        $accountTypeId = $this->getCurrentUserAccountTypeId();
        return $this->getDisplayTitleForAccountType($accountTypeId);
    }

    /**
     * Detect current user's account type automatically
     *
     * @return int|null
     */
    private function getCurrentUserAccountTypeId()
    {
        // For API users (Account model)
        if (request()->has('software_type') || request()->has('account_id')) {
            return $this->getAccountTypeFromRequest();
        }

        // For web users, check if there's an account type context in session or request
        if (request()->has('account_type_id')) {
            return request()->input('account_type_id');
        }

        // Check session for persistent account type selection
        if (session()->has('current_account_type_id')) {
            return session('current_account_type_id');
        }

        return null; // Fall back to global ordering
    }

    /**
     * Get account type from API request context
     *
     * @return int|null
     */
    private function getAccountTypeFromRequest()
    {
        $softwareType = request()->input('software_type');
        $accountId = request()->input('account_id');

        if ($accountId) {
            $account = \App\Models\Account::find($accountId);
            if ($account) {
                if ($softwareType == \App\Models\Account::SOFTWARE_MAMNON && $account->is_mamnon) {
                    return $account->mamnon_account_type_id;
                }
                if ($softwareType == \App\Models\Account::SOFTWARE_TIEUHOC && $account->is_tieuhoc) {
                    return $account->tieuhoc_account_type_id;
                }
            }
        }

        return null;
    }

    /**
     * Get the base title without the "Bài X - " prefix
     *
     * @return string
     */
    public function getBaseTitleAttribute()
    {
        return preg_replace('/^Bài\s+\d+\s*-\s*/', '', $this->title);
    }

    /**
     * Set the order for a specific account type
     *
     * @param int $accountTypeId
     * @param int|null $order
     * @return void
     */
    public function setOrderForAccountType($accountTypeId, $order)
    {
        AccountTypeMaterialOrder::updateOrCreate(
            [
                'account_type_id' => $accountTypeId,
                'material_id' => $this->id
            ],
            [
                'order' => $order
            ]
        );
    }

    /**
     * Update the title based on the order (backward compatibility)
     *
     * @param int|null $order
     * @return void
     */
    public function updateTitleWithOrder($order = null)
    {
        $order = $order ?? $this->order;

        if ($order) {
            $baseTitle = $this->getBaseTitleAttribute();
            $this->title = "Bài " . $order . " - " . $baseTitle;
        }
    }

    /**
     * Update the title based on the order for a specific account type
     *
     * @param int $accountTypeId
     * @param int|null $order
     * @return void
     */
    public function updateTitleWithOrderForAccountType($accountTypeId, $order = null)
    {
        $order = $order ?? $this->getOrderForAccountType($accountTypeId);

        if ($order) {
            $baseTitle = $this->getBaseTitleAttribute();
            $this->title = "Bài " . $order . " - " . $baseTitle;
            $this->save();
        }
    }
}
