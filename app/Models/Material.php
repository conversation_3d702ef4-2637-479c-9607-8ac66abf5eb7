<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Material extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['lesson_id', 'title', 'type', 'content', 'content_url', 'deleted_at', 'document_url', 'order'];

    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    /**
     * Get the display title with order prefix if order is set
     *
     * @return string
     */
    public function getDisplayTitleAttribute()
    {
        if ($this->order) {
            // Remove existing "Bài X - " prefix if it exists to avoid duplication
            $cleanTitle = preg_replace('/^Bài\s+\d+\s*-\s*/', '', $this->title);
            return "Bài " . $this->order . " - " . $cleanTitle;
        }

        return $this->title;
    }

    /**
     * Get the base title without the "Bài X - " prefix
     *
     * @return string
     */
    public function getBaseTitleAttribute()
    {
        return preg_replace('/^Bài\s+\d+\s*-\s*/', '', $this->title);
    }

    /**
     * Update the title based on the order
     *
     * @param int|null $order
     * @return void
     */
    public function updateTitleWithOrder($order = null)
    {
        $order = $order ?? $this->order;

        if ($order) {
            $baseTitle = $this->getBaseTitleAttribute();
            $this->title = "Bài " . $order . " - " . $baseTitle;
        }
    }
}
