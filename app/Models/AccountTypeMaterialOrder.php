<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountTypeMaterialOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_type_id',
        'material_id',
        'order'
    ];

    public function accountType()
    {
        return $this->belongsTo(AccountType::class);
    }

    public function material()
    {
        return $this->belongsTo(Material::class);
    }
}
