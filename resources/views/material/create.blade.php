@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.add.material'))

@section('menu_pagina')

    <li role="presentation">
        <a href="{{ route('material') }}" class="link_menu_page">
            <i class="fa fa-user"></i> {{ __('messages.index.material') }}
        </a>
    </li>

@endsection

@section('content')

    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('material.store') }}" method="post">
                        {{ csrf_field() }}
                        <input type="hidden" name="active" value="1">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('title') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.title') }}</label>
                                    <input type="text" name="title" class="form-control" placeholder="{{ __('messages.title') }}" required="" value="{{ old('title', $title) }}" autofocus>
                                    @if($errors->has('title'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('title') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('order') ? 'has-error' : '' }}">
                                    <label for="order">{{ __('messages.order') }}</label>
                                    <input type="number" name="order" class="form-control" placeholder="{{ __('messages.order') }}" value="{{ old('order') }}" min="1">
                                    <small class="help-block">Leave empty for no automatic numbering. If set, title will be prefixed with "Bài [order] - "</small>
                                    @if($errors->has('order'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('order') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
{{--                            <div class="col-lg-6">--}}
{{--                                <div class="form-group {{ $errors->has('type') ? 'has-error' : '' }}">--}}
{{--                                    <label for="nome">{{ __('messages.type') }}</label>--}}
{{--                                    <select name="type" id="type" class="form-control select2" data-placeholder="{{ __('messages.type') }}" required="">--}}
{{--                                        <option value="video"> video </option>--}}
{{--                                        <option value="document"> document </option>--}}
{{--                                    </select>--}}
{{--                                    @if($errors->has('type'))--}}
{{--                                        <span class="help-block">--}}
{{--                                            <strong>{{ $errors->first('type') }}</strong>--}}
{{--                                        </span>--}}
{{--                                    @endif--}}
{{--                                </div>--}}
{{--                            </div>--}}
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('content') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.content') }}</label>
                                    <input type="text" name="content" class="form-control" placeholder="{{ __('messages.content') }}" required="" value="{{ old('content', $content) }}" autofocus>
                                    @if($errors->has('content'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('content') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('content_url') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.content_url') }}</label>
                                    <input type="text" name="content_url" class="form-control" placeholder="{{ __('messages.content_url') }}" required="" value="{{ old('content_url') }}" autofocus>
                                    @if($errors->has('content_url'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('content_url') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('document_url') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.document_url') }}</label>
                                    <input type="text" name="document_url" class="form-control" placeholder="{{ __('messages.document_url') }}" required="" value="{{ old('document_url') }}" autofocus>
                                    @if($errors->has('document_url'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('document_url') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('lesson') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.lesson') }}</label>
                                    <select name="lesson" id="lesson" class="form-control select2" data-placeholder="{{ __('messages.lesson') }}" required="">
                                        @foreach($lessons as $lesson)
                                            <option value="{{ $lesson->id }}"> {{ $lesson->course->title_en . ' - ' . $lesson->title }} </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('lesson'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('lesson') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label for="created_at">{{ __('messages.added_date') }}</label>
                                    <input type="text" class="form-control" value="{{ __('messages.will_be_set_to_current_date') }}" readonly>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <button type="submit" class="btn btn-primary pull-right"><i class="fa fa-fw fa-plus"></i> {{ __('messages.action.add') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('layout_js')

<script>
    $(function(){
        $('.select2').select2({
            "language": {
                "noResults": function(){
                    return "Nenhum registro encontrado.";
                }
            }
        });
    });

</script>

@endsection
