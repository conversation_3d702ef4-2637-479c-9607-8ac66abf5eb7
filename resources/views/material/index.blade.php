@extends('layouts.AdminLTE.index')

@section('icon_page', 'user')

@section('title', __('messages.index.material'))

@section('menu_pagina')

<li role="presentation">
    <a href="{{ route('material.create') }}" class="link_menu_page">
        <i class="fa fa-plus"></i> {{ __('messages.add.material') }}
    </a>
</li>

@endsection

@section('content')

<div class="box box-primary">
    <div class="box-body">
        <div class="row">
            <form action="{{ route('material') }}" method="get">
                <div class="col-lg-3">
                    <div class="form-group {{ $errors->has('name') ? 'has-error' : '' }}">
                        <label for="nome">{{ __('messages.search') }}</label>
                        <input type="text" name="search" class="form-control" placeholder="{{ __('messages.search') }}" value="{{ $keyword }}">
                        @if(isset($accountTypeId))
                            <input type="hidden" name="account_type_id" value="{{ $accountTypeId }}">
                        @endif
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group">
                        <label for="account_type_id">{{ __('messages.account_type') }}</label>
                        <select name="account_type_id" class="form-control" onchange="this.form.submit()">
                            <option value="">{{ __('messages.all_account_types') }}</option>
                            @if(isset($accountTypes))
                                @foreach($accountTypes as $accountType)
                                    <option value="{{ $accountType->id }}" {{ isset($accountTypeId) && $accountTypeId == $accountType->id ? 'selected' : '' }}>
                                        {{ $accountType->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                        @if(isset($keyword))
                            <input type="hidden" name="search" value="{{ $keyword }}">
                        @endif
                    </div>
                </div>
                <div class="col-lg-1">
                    <label for="nome">{{ __('messages.filter') }} / {{ __('messages.search') }}</label>
                    <button type="submit" class="btn btn-google pull-right"><i class="fa fa-fw fa-search"></i> {{ __('messages.search') }}</button>
                </div>

            </form>
            <!-- modal -->

        </div>
    </div>
    <div class="box-body">
        <div class="row">
            <div class="col-md-12">
                <div class="table-responsive">
                    <table id="tabelapadrao" class="table table-condensed table-bordered table-hover">
                        <thead>
                        <tr>
                            <th></th>
                            <th class="col-md-1">
                                <a href="{{ route('material', array_merge(request()->except(['sort', 'direction']), ['sort' => 'order', 'direction' => (isset($sortField) && $sortField == 'order' && $sortDirection == 'asc') ? 'desc' : 'asc'])) }}">
                                    {{ __('messages.order') }}
                                    @if(isset($sortField) && $sortField == 'order')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th class="col-md-2">
                                <a href="{{ route('material', array_merge(request()->except(['sort', 'direction']), ['sort' => 'title', 'direction' => (isset($sortField) && $sortField == 'title' && $sortDirection == 'asc') ? 'desc' : 'asc'])) }}">
                                    {{ __('messages.title') }}
                                    @if(isset($sortField) && $sortField == 'title')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th class="col-md-2">
                                <a href="{{ route('material', array_merge(request()->except(['sort', 'direction']), ['sort' => 'content', 'direction' => (isset($sortField) && $sortField == 'content' && $sortDirection == 'asc') ? 'desc' : 'asc'])) }}">
                                    {{ __('messages.content') }}
                                    @if(isset($sortField) && $sortField == 'content')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th class="col-md-2">{{ __('messages.content_url') }}</th>
                            <th class="col-md-2">{{ __('messages.document_url') }}</th>
                            <th class="col-md-2">
                                <a href="{{ route('material', array_merge(request()->except(['sort', 'direction']), ['sort' => 'created_at', 'direction' => (isset($sortField) && $sortField == 'created_at' && $sortDirection == 'asc') ? 'desc' : 'asc'])) }}">
                                    {{ __('messages.added_date') }}
                                    @if(isset($sortField) && $sortField == 'created_at')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th class="col-md-2">{{ __('messages.lesson') }}</th>
                            <th class="col-md-2">{{ __('messages.course') }}</th>
                            <th class="text-center">{{ __('messages.action') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php $cnt = 1; @endphp
                        @foreach($materials as $material)
                        <tr>
                            <td>{{ $cnt++ }}</td>
                            <td>
                                @if(isset($accountTypeId))
                                    {{ $material->getOrderForAccountType($accountTypeId) ?? '-' }}
                                @else
                                    {{ $material->order ?? '-' }}
                                @endif
                            </td>
                            <td>
                                {{ $material->display_title }}
                            </td>
                            <td>{{ $material->content }}</td>
                            <td>{{ $material->content_url }}</td>
                            <td>
                                @if($material->document_url != null)
                                    <a href="{{ $material->document_url }}" target="_blank">{{ __('messages.watch_document') }}</a>
                                @endif
                            </td>
                            <td>{{ $material->created_at ? $material->created_at->format('d-m-Y') : '' }}</td>
                            <td>{{ $material->lesson->title }}</td>
                            <td>{{ $material->lesson->course->title_en }}</td>

                            <td class="text-center">
                                <a class="btn btn-warning  btn-xs" href="{{ route('material.edit', $material->id) }}" title="Edit {{ $material->title }}"><i class="fa fa-pencil"></i></a>
                                <a class="btn btn-danger  btn-xs" href="#" title="Delete {{ $material->title}}" data-toggle="modal" data-target="#modal-delete-{{ $material->id }}"><i class="fa fa-trash"></i></a>
                            </td>
                        </tr>
                        <div class="modal fade" id="modal-delete-{{ $material->id }}">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                                    </div>
                                    <div class="modal-body">
                                        <p>{{ __('messages.do_you_want_delete') }} ({{ $material->title }}) ?</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default pull-left" data-dismiss="modal">Cancel</button>
                                        <a href="{{ route('material.destroy', $material->id) }}"><button type="button" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-12 text-center">
                {{ $materials->links() }}
            </div>
        </div>
    </div>
</div>

<script>
</script>

@endsection
