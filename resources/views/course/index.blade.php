@extends('layouts.AdminLTE.index')

@section('icon_page', 'user')

@section('title')
    @if(!isset($viewMode) || $viewMode == 'course')
        {{ __('messages.index.course') }}
    @else
        {{ __('messages.index.material') }}
    @endif
@endsection

@section('css')
<style>
    /* Ensure table columns have appropriate widths */
    #tabelapadrao th, #tabelapadrao td {
        padding: 12px 8px; /* Increased padding for taller rows */
        vertical-align: middle;
        border: 1px solid #ddd;
    }

    /* Allow line breaks in specific columns */
    #tabelapadrao td.text-center {
        white-space: normal;
        line-height: 1.5; /* Increased line height for better readability */
    }

    /* Add some spacing between lines in cells with multiple entries */
    #tabelapadrao td br {
        margin-bottom: 5px;
        content: "";
        display: block;
        margin-top: 5px;
    }

    /* Improve table appearance */
    #tabelapadrao {
        border-collapse: collapse; /* Changed to collapse for better border rendering */
        border-spacing: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid #ddd; /* Add outer border */
    }

    #tabelapadrao th {
        background-color: #ffffff;
        color: #000000;
        font-weight: bold;
        padding: 14px 10px; /* Larger padding for headers */
        border-bottom: 2px solid #ddd;
    }

    /* Alternate row colors for better readability */
    #tabelapadrao tbody tr:nth-child(odd) {
        background-color: #f9f9f9;
    }

    #tabelapadrao tbody tr:hover {
        background-color: #e8f4fc;
    }

    /* Add visible borders between rows */
    #tabelapadrao tbody tr {
        border-bottom: 1px solid #ddd;
    }

    /* Make the table cells more readable with visible borders */
    #tabelapadrao td, #tabelapadrao th {
        vertical-align: middle;
        border-top: 1px solid #ddd;
        border-right: 1px solid #ddd; /* Add vertical borders between columns */
        border-bottom: 1px solid #ddd; /* Ensure bottom borders are visible */
    }

    /* Remove border from last column */
    #tabelapadrao td:last-child, #tabelapadrao th:last-child {
        border-right: none;
    }

    /* Make sure the table is responsive */
    .table-responsive {
        overflow-x: auto;
    }

    /* Ensure buttons are properly sized and spaced */
    .btn-xs {
        padding: 5px 10px;
        font-size: 12px;
        line-height: 1.5;
        border-radius: 3px;
        margin: 0 3px;
        transition: all 0.2s ease;
    }

    /* Improve button appearance */
    .btn-default.btn-xs {
        background-color: #f8f9fa;
        border-color: #ddd;
    }

    .btn-default.btn-xs:hover {
        background-color: #e9ecef;
        border-color: #ccc;
    }

    .btn-danger.btn-xs {
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .btn-danger.btn-xs:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    /* Sort icons styling */
    th a {
        color: #000000;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    th a:hover {
        color: #333333;
        text-decoration: none;
    }

    th a i {
        margin-left: 5px;
    }
</style>
@endsection

@section('menu_pagina')

<li role="presentation">
    @if(!isset($viewMode) || $viewMode == 'course')
    <a href="{{ route('course.create') }}" class="link_menu_page">
        <i class="fa fa-plus"></i> {{ __('messages.add.course') }}
    </a>
    @else
    <a href="{{ route('material.create') }}" class="link_menu_page">
        <i class="fa fa-plus"></i> {{ __('messages.add.material') }}
    </a>
    @endif
</li>

@endsection

@section('content')

<div class="box box-primary">
    <div class="box-body">
        <div class="row">
            <form action="{{ route('course') }}" method="get" id="search-form">
                <div class="col-lg-3">
                    <div class="form-group">
                        <label for="filter_course">{{ __('messages.filter_by_course') }}</label>
                        <select name="filter_course" id="filter_course" class="form-control select2" data-placeholder="{{ __('messages.filter_by_course') }}">
                            <option value="">{{ __('messages.all') }}</option>
                            @if(isset($allCourses))
                                @foreach($allCourses as $filterCourse)
                                    <option value="{{ $filterCourse->id }}" @if(isset($filterCourseId) && $filterCourseId == $filterCourse->id) selected @endif>{{ $filterCourse->title_en }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                @if(isset($viewMode) && $viewMode == 'material')
                <div class="col-lg-3">
                    <div class="form-group">
                        <label for="account_type_id">{{ __('messages.account_type') }}</label>
                        <select name="account_type_id" class="form-control" onchange="this.form.submit()">
                            <option value="">{{ __('messages.all_account_types') }}</option>
                            @if(isset($accountTypes))
                                @foreach($accountTypes as $accountType)
                                    <option value="{{ $accountType->id }}" {{ isset($accountTypeId) && $accountTypeId == $accountType->id ? 'selected' : '' }}>
                                        {{ $accountType->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                @endif
                <div class="col-lg-{{ isset($viewMode) && $viewMode == 'material' ? '3' : '4' }}">
                    <div class="form-group {{ $errors->has('name') ? 'has-error' : '' }}">
                        <label for="nome">{{ __('messages.search') }}</label>
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="{{ __('messages.search') }}" value="{{ $keyword }}">
                            <span class="input-group-btn">
                                <button type="submit" class="btn btn-google"><i class="fa fa-fw fa-search"></i> {{ __('messages.search') }}</button>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- View mode toggle -->
                <input type="hidden" name="view_mode" id="view_mode" value="{{ isset($viewMode) ? $viewMode : 'course' }}">

                <div class="col-lg-9">
                    <div class="row" style="margin-top: 20px;">
                        <div class="col-lg-4">
                            <div class="form-group">
                                <label for="view_toggle">{{ __('messages.view_mode') }}</label>
                                <div class="btn-group btn-toggle btn-block">
                                    <button type="button" class="btn {{ isset($viewMode) && $viewMode == 'course' ? 'btn-primary active' : 'btn-default' }}" data-mode="course" style="width: 50%;">{{ __('messages.course') }}</button>
                                    <button type="button" class="btn {{ isset($viewMode) && $viewMode == 'material' ? 'btn-primary active' : 'btn-default' }}" data-mode="material" style="width: 50%;">{{ __('messages.materials') }}</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="form-group">
                                <label>{{ __('messages.import_export') }}</label>
                                <div class="btn-group btn-block">
                                    @if(isset($viewMode) && $viewMode == 'material')
                                    <button type="button" class="btn btn-default" style="width: 50%;" onclick="document.getElementById('export-materials-form').submit();">
                                        <i class="fa fa-fw fa-download"></i> {{ __('messages.action.export') }}
                                    </button>
                                    <a data-toggle="modal" data-target="#modal-import-materials" class="btn btn-default" style="width: 50%;">
                                        <i class="fa fa-fw fa-upload"></i> {{ __('messages.action.import') }}
                                    </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="form-group">
                                <label>{{ __('messages.bulk_actions') }}</label>
                                <div class="btn-group btn-block">
                                    @if(!isset($viewMode) || $viewMode == 'course')
                                    <button type="button" class="btn btn-danger btn-block" data-toggle="modal" data-target="#modal-bulk-delete-course">
                                        <i class="fa fa-fw fa-trash"></i> {{ __('messages.action.bulk_delete') }}
                                    </button>
                                    @else
                                    <button type="button" class="btn btn-danger btn-block" data-toggle="modal" data-target="#modal-bulk-delete-material">
                                        <i class="fa fa-fw fa-trash"></i> {{ __('messages.action.bulk_delete') }}
                                    </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Export Materials Form (outside search form to avoid nesting) -->
            <form id="export-materials-form" action="{{ route('course.export_materials') }}" method="get" style="display: none;">
            </form>

            <!-- Bulk Delete Course Modal -->
            <div class="modal fade" id="modal-bulk-delete-course">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                        </div>
                        <div class="modal-body">
                            <p>{{ __('messages.do_you_want_delete_selected') }}</p>
                        </div>
                        <div class="modal-footer">
                            <form action="{{ route('course') }}/bulk-destroy" method="post">
                                {{ csrf_field() }}
                                <input type="hidden" value="" id="bulk_destroy_course_value" name="ids">
                                <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                <button type="submit" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bulk Delete Material Modal -->
            <div class="modal fade" id="modal-bulk-delete-material">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                        </div>
                        <div class="modal-body">
                            <p>{{ __('messages.do_you_want_delete_selected') }}</p>
                        </div>
                        <div class="modal-footer">
                            <form action="{{ route('material') }}/bulk-destroy" method="post">
                                {{ csrf_field() }}
                                <input type="hidden" value="" id="bulk_destroy_material_value" name="ids">
                                <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                <button type="submit" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Material Import Modal -->
            <div class="modal fade" id="modal-import-materials">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.download_template') }}!!</h4>
                        </div>
                        <div class="modal-footer">
                            <form class="pull-left" action="{{ route('course.export_materials_template') }}" method="get" >
                                <button type="submit" class="btn btn-file btn-xs"><i class="fa fa-fw fa-download"></i> {{ __('messages.action.export_template') }}</button>
                            </form>

                            <form class="pull-right" style="display: flex; flex-direction: column;" action="{{ route('course.import_materials') }}" method="post" enctype="multipart/form-data">
                                {{ csrf_field() }}
                                <input type="file" value="" id="file-import-materials" name="file" style="margin-bottom: 5px; max-width: 200px;">
                                <div style="margin-bottom: 5px;">
                                    <label style="font-weight: normal; font-size: 12px;">
                                        <input type="checkbox" name="auto_numbering" value="1" checked>
                                        Auto-number materials (extract from content)
                                    </label>
                                </div>
                                @if(isset($accountTypes) && count($accountTypes) > 0)
                                <div style="margin-bottom: 5px;">
                                    <label style="font-weight: normal; font-size: 12px; display: block;">Account Type (for order):</label>
                                    <select name="account_type_id" style="max-width: 200px; font-size: 12px;">
                                        <option value="">{{ __('messages.global_order') }}</option>
                                        @foreach($accountTypes as $accountType)
                                            <option value="{{ $accountType->id }}" {{ isset($accountTypeId) && $accountTypeId == $accountType->id ? 'selected' : '' }}>
                                                {{ $accountType->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @endif
                                <button type="submit" class="btn btn-file btn-xs"><i class="fa fa-fw fa-upload"></i> {{ __('messages.action.import') }}</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="box-body">
        <div class="row">
            <div class="col-md-12">
                <div class="table-responsive">
                    @if(!isset($viewMode) || $viewMode == 'course')
                    <!-- Course View -->
                    <table id="tabelapadrao" class="table table-hover">
                        <thead>
                        <tr>
                            <th style="width: 30px;"><input type="checkbox" id="checkbox_master"></th>
                            <th style="width: 50px;">{{ __('messages.count') }}</th>
                            <th style="width: 150px;">
                                <a href="{{ route('course', array_merge(request()->except(['sort', 'direction']), ['sort' => 'title_en', 'direction' => (isset($sortField) && $sortField == 'title_en' && $sortDirection == 'asc') ? 'desc' : 'asc', 'view_mode' => 'course'])) }}">
                                    {{ __('messages.title') }}
                                    @if(isset($sortField) && $sortField == 'title_en')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th style="width: 300px;">
                                <a href="{{ route('course', array_merge(request()->except(['sort', 'direction']), ['sort' => 'description_en', 'direction' => (isset($sortField) && $sortField == 'description_en' && $sortDirection == 'asc') ? 'desc' : 'asc', 'view_mode' => 'course'])) }}">
                                    {{ __('messages.content') }}
                                    @if(isset($sortField) && $sortField == 'description_en')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th style="width: 120px;">{{ __('messages.content_url') }}</th>
                            <th style="width: 120px;">{{ __('messages.document_url') }}</th>
                            <th style="width: 120px;">
                                <a href="{{ route('course', array_merge(request()->except(['sort', 'direction']), ['sort' => 'created_at', 'direction' => (isset($sortField) && $sortField == 'created_at' && $sortDirection == 'asc') ? 'desc' : 'asc', 'view_mode' => 'course'])) }}">
                                    {{ __('messages.created_at') }}
                                    @if(isset($sortField) && $sortField == 'created_at')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th style="width: 100px;">{{ __('messages.action') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php $cnt = 1; @endphp
                        @foreach($courses as $course)
                        <tr>
                            <td><input type="checkbox" class="checkbox_row" data-id="{{$course->id}}"></td>
                            <td>{{ $cnt++ }}</td>
                            <td>{{ $course->title_en }}</td>
                            <td>{{ $course->description_en }}</td>
                            <td><img src="{{ asset('uploads/courses/' . $course->image) }}" height="30%" width="30%"  alt=""/></td>
                            <td><img src="{{ asset('uploads/courses/thumbnails/' . $course->thumbnail_image) }}" height="30%" width="30%"  alt=""/></td>
                            <td class="text-center">{{$course->created_at->format('d/m/Y H:i')}}</td>

                            <td class="text-center">
                                <a class="btn btn-default btn-xs" href="{{ route('course.edit', $course->id) }}" title="Edit {{ $course->title_en }}"><i class="fa fa-pencil"></i></a>
                                <a class="btn btn-danger btn-xs" href="#" title="Delete {{ $course->title_en }}" data-toggle="modal" data-target="#modal-delete-{{ $course->id }}"><i class="fa fa-trash"></i></a>
                            </td>
                        </tr>
                        <div class="modal fade" id="modal-delete-{{ $course->id }}">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                                    </div>
                                    <div class="modal-body">
                                        <p>{{ __('messages.do_you_want_delete') }} ({{ $course->title_en }}) ?</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                        <a href="{{ route('course.destroy', $course->id) }}"><button type="button" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                        </tbody>
                    </table>
                    <div class="col-md-12 text-center">
                        {{ $courses->appends(request()->input())->links() }}
                    </div>
                    @else
                    <!-- Material View -->
                    <table id="tabelapadrao" class="table table-hover">
                        <thead>
                        <tr>
                            <th style="width: 30px;"><input type="checkbox" id="checkbox_master_material"></th>
                            <th style="width: 50px;">{{ __('messages.count') }}</th>
                            <th style="width: 60px;">
                                <a href="{{ route('course', array_merge(request()->except(['sort', 'direction']), ['sort' => 'order', 'direction' => (isset($sortField) && $sortField == 'order' && $sortDirection == 'asc') ? 'desc' : 'asc', 'view_mode' => 'material'])) }}">
                                    {{ __('messages.order') }}
                                    @if(isset($sortField) && $sortField == 'order')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th style="width: 150px;">
                                <a href="{{ route('course', array_merge(request()->except(['sort', 'direction']), ['sort' => 'title', 'direction' => (isset($sortField) && $sortField == 'title' && $sortDirection == 'asc') ? 'desc' : 'asc', 'view_mode' => 'material'])) }}">
                                    {{ __('messages.title') }}
                                    @if(isset($sortField) && $sortField == 'title')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th style="width: 200px;">
                                <a href="{{ route('course', array_merge(request()->except(['sort', 'direction']), ['sort' => 'content', 'direction' => (isset($sortField) && $sortField == 'content' && $sortDirection == 'asc') ? 'desc' : 'asc', 'view_mode' => 'material'])) }}">
                                    {{ __('messages.content') }}
                                    @if(isset($sortField) && $sortField == 'content')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th style="width: 150px;">{{ __('messages.content_url') }}</th>
                            <th style="width: 150px;">{{ __('messages.document_url') }}</th>
                            <th style="width: 120px;">
                                <a href="{{ route('course', array_merge(request()->except(['sort', 'direction']), ['sort' => 'created_at', 'direction' => (isset($sortField) && $sortField == 'created_at' && $sortDirection == 'asc') ? 'desc' : 'asc', 'view_mode' => 'material'])) }}">
                                    {{ __('messages.added_date') }}
                                    @if(isset($sortField) && $sortField == 'created_at')
                                        <i class="fa fa-sort-{{ $sortDirection == 'asc' ? 'up' : 'down' }}"></i>
                                    @else
                                        <i class="fa fa-sort"></i>
                                    @endif
                                </a>
                            </th>
                            <th style="width: 120px;">{{ __('messages.lesson') }}</th>
                            <th style="width: 120px;">{{ __('messages.course') }}</th>
                            <th style="width: 100px;">{{ __('messages.action') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php $cnt = 1; @endphp
                        @foreach($materials as $material)
                        <tr>
                            <td><input type="checkbox" class="checkbox_row_material" data-id="{{$material->id}}"></td>
                            <td>{{ $cnt++ }}</td>
                            <td>
                                @if(isset($accountTypeId))
                                    {{ $material->getOrderForAccountType($accountTypeId) ?? '-' }}
                                @else
                                    {{ $material->order ?? '-' }}
                                @endif
                            </td>
                            <td>
                                {{ $material->display_title }}
                            </td>
                            <td>{{ $material->content }}</td>
                            <td>{{ $material->content_url }}</td>
                            <td>
                                @if($material->document_url != null)
                                    <a href="{{ $material->document_url }}" target="_blank">{{ __('messages.watch_document') }}</a>
                                @endif
                            </td>
                            <td>{{ $material->created_at ? $material->created_at->format('d/m/Y H:i') : '' }}</td>
                            <td>{{ $material->lesson->title }}</td>
                            <td>{{ $material->lesson->course->title_en }}</td>

                            <td class="text-center">
                                <a class="btn btn-default btn-xs" href="{{ route('material.edit', $material->id) }}" title="Edit {{ $material->title }}"><i class="fa fa-pencil"></i></a>
                                <a class="btn btn-danger btn-xs" href="#" title="Delete {{ $material->title}}" data-toggle="modal" data-target="#modal-delete-material-{{ $material->id }}"><i class="fa fa-trash"></i></a>
                            </td>
                        </tr>
                        <div class="modal fade" id="modal-delete-material-{{ $material->id }}">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                                    </div>
                                    <div class="modal-body">
                                        <p>{{ __('messages.do_you_want_delete') }} ({{ $material->title }}) ?</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                        <a href="{{ route('material.destroy', $material->id) }}"><button type="button" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                        </tbody>
                    </table>
                    <div class="col-md-12 text-center">
                        {{ $materials->appends(request()->input())->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Toggle button functionality
    $('.btn-toggle button').on('click', function() {
        const mode = $(this).data('mode');
        $('#view_mode').val(mode);

        // Update button styles
        $('.btn-toggle button').removeClass('btn-primary active').addClass('btn-default');
        $(this).removeClass('btn-default').addClass('btn-primary active');

        // Submit the form to switch views
        $('#search-form').submit();
    });

    $(document).ready(function () {
        // Course checkbox master functionality
        $('#checkbox_master').on('click', function(e) {
            if($(this).is(':checked',true)) {
                $(".checkbox_row").prop('checked', true);
            } else {
                $(".checkbox_row").prop('checked',false);
            }

            let allVals = [];
            $(".checkbox_row:checked").each(function() {
                allVals.push($(this).attr('data-id'));
            });

            $('#bulk_destroy_course_value').val("");
            $('#bulk_destroy_course_value').val(allVals.join(','));
        });

        // Individual course checkbox functionality
        $('.checkbox_row').on('click', function(e) {
            let allVals = [];
            $(".checkbox_row:checked").each(function() {
                allVals.push($(this).attr('data-id'));
            });

            $('#bulk_destroy_course_value').val("");
            $('#bulk_destroy_course_value').val(allVals.join(','));
        });

        // Material checkbox master functionality
        $('#checkbox_master_material').on('click', function(e) {
            if($(this).is(':checked',true)) {
                $(".checkbox_row_material").prop('checked', true);
            } else {
                $(".checkbox_row_material").prop('checked',false);
            }

            let allVals = [];
            $(".checkbox_row_material:checked").each(function() {
                allVals.push($(this).attr('data-id'));
            });

            $('#bulk_destroy_material_value').val("");
            $('#bulk_destroy_material_value').val(allVals.join(','));
        });

        // Individual material checkbox functionality
        $('.checkbox_row_material').on('click', function(e) {
            let allVals = [];
            $(".checkbox_row_material:checked").each(function() {
                allVals.push($(this).attr('data-id'));
            });

            $('#bulk_destroy_material_value').val("");
            $('#bulk_destroy_material_value').val(allVals.join(','));
        });
    });
</script>

@endsection
